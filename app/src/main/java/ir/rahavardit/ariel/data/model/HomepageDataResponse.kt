package ir.rahavardit.ariel.data.model

import com.google.gson.annotations.SerializedName

/**
 * Data class representing the new homepage statistics response from the API.
 */
data class HomepageDataResponse(
    val years: List<Int>,
    @SerializedName("names_of_months__persian")
    val namesOfMonthsPersian: List<String>,
    val chosenyear: Int,
    val chosenmonthstart: Int,
    val chosenmonthend: Int,
    @SerializedName("chosenmonthstart__zeroed")
    val chosenmonthstartZeroed: String,
    @SerializedName("chosenmonthend__zeroed")
    val chosenmonthendZeroed: String,
    @SerializedName("chosenyear__persian")
    val chosenyearPersian: String,
    @SerializedName("chosenmonthstart__zeroed__persian")
    val chosenmonthstartZeroedPersian: String,
    @SerializedName("chosenmonthend__zeroed__persian")
    val chosenmonthendZeroedPersian: String,
    @SerializedName("income_objects")
    val incomeObjects: List<IncomeObject>,
    @SerializedName("expenditure_objects")
    val expenditureObjects: List<ExpenditureObject>,
    @SerializedName("event_objects")
    val eventObjects: List<EventObject>
)

/**
 * Data class representing an income object.
 */
data class IncomeObject(
    val id: Int,
    val mode: String,
    val title: String,
    val author: Author,
    val amount: Int,
    val year: Int,
    val month: Int,
    val day: Int,
    val bank: Bank,
    val category: HomepageCategory,
    val tags: List<Int>,
    @SerializedName("tags_names")
    val tagsNames: List<String>,
    @SerializedName("short_uuid")
    val shortUuid: String,
    val active: Boolean,
    val created: String,
    @SerializedName("created_jalali")
    val createdJalali: String,
    val updated: String
)

/**
 * Data class representing an expenditure object.
 */
data class ExpenditureObject(
    val id: Int,
    val mode: String,
    val title: String,
    val author: Author,
    val amount: Int,
    val year: Int,
    val month: Int,
    val day: Int,
    val bank: Bank,
    val category: HomepageCategory,
    val tags: List<Int>,
    @SerializedName("tags_names")
    val tagsNames: List<String>,
    @SerializedName("short_uuid")
    val shortUuid: String,
    val active: Boolean,
    val created: String,
    @SerializedName("created_jalali")
    val createdJalali: String,
    val updated: String
)

/**
 * Data class representing an event object.
 */
data class EventObject(
    val id: Int,
    val title: String,
    val author: Author,
    val year: Int,
    val month: Int,
    val day: Int,
    val active: Boolean,
    @SerializedName("short_uuid")
    val shortUuid: String,
    val created: String,
    @SerializedName("created_jalali")
    val createdJalali: String,
    val updated: String
)

/**
 * Data class representing a bank.
 */
data class Bank(
    val id: Int,
    val title: String,
    @SerializedName("short_uuid")
    val shortUuid: String
)

/**
 * Data class representing a category for homepage objects.
 */
data class HomepageCategory(
    val id: Int,
    val title: String,
    @SerializedName("short_uuid")
    val shortUuid: String
)
